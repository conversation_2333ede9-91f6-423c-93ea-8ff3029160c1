from fastapi import APIRouter
from openai import BaseModel
from urllib.parse import unquote

from Agents.ImproveInfoAgent import ImproveInfoAgent
from Models.dto.TaskInfoDto import TaskInfoDto

from Agents.TalentAgent import TalentAgent
from Agents.TalentAgentProcessing import TalentAgentProcessing
from Models.AjaxResult import AjaxResult
from Services.TaskServer.TalentParseWorker import TalentParseWorker

# 添加项目根目录到系统路径
router = APIRouter(prefix="/agentService/api/talent", tags=["talent"])


class TalentInfo(BaseModel):
    id: int = None
    content: str = None
    question: str = None


@router.post("/info")
async def chat(request: TalentInfo):
    try:
        if not request.question:
            raise AjaxResult.error()

        agent = TalentAgent()
        row = await agent.chat_for_answer(request.question, request.id)
        return AjaxResult.success(row)
    except Exception as e:
        print(f"Error in stream_chat: {str(e)}")
        return AjaxResult.error(data=0, message=str(e))


# 简历解析
@router.get("/parse")
async def parse(fileUrl: str = None):
    try:
        if not fileUrl:
            raise AjaxResult.error("文件地址不能为空")
        # 调用文件解析方法
        content = await TalentParseWorker(fileUrl)._task()
        if not content:
            raise AjaxResult.error("文件解析失败")
        agent = TalentAgent()
        talent_info = agent._formatting(content)
        return AjaxResult.success(talent_info)
    except Exception as e:
        print(f"Error in stream_chat: {str(e)}")
        return AjaxResult.error(str(e))

# 简历评分
@router.get("/rating")
def rating():
    try:
        # TODO: 评分逻辑,直接传json评分结果
        return AjaxResult.success()
    except Exception as e:
        print(f"Error in stream_chat: {str(e)}")
        return AjaxResult.error(str(e))


# 简历解析
@router.get("/parseDto")
async def parseDto(fileUrl: str = None):
    try:
        if not fileUrl:
            raise AjaxResult.error("文件地址不能为空")
        # 调用文件解析方法
        content = await TalentParseWorker(fileUrl)._task()
        if not content:
            raise AjaxResult.error("文件解析失败")
        agent = TalentAgentProcessing()
        talent_info = agent._formatting(content,boss_flag=True)
        return AjaxResult.success(talent_info)
    except Exception as e:
        print(f"Error in stream_chat: {str(e)}")
        return AjaxResult.error(str(e))

# 简历分析
@router.get("/analyse")
async def analyse(fileUrl: str = None, taskId: int = None, id: int = None):
    try:
        if not fileUrl:
            raise AjaxResult.error("文件地址不能为空")
        agent = TalentAgent()

        # 标记解析中状态=1
        if taskId is not None:
            agent.updateFileStatus(id, 1)

        # 调用文件解析方法
        content = await TalentParseWorker(fileUrl)._task()
        if not content:
            # 解析失败，标记失败状态=2
            if taskId is not None:
                agent.updateFileStatus(id, 2)
            raise AjaxResult.error("文件解析失败")

        # 格式化并评分
        talent_info = agent._formatting(content)
        if talent_info is None:
            # 解析失败，标记失败状态=2
            if taskId is not None:
                agent.updateFileStatus(id, 2)
            raise AjaxResult.error("简历解析失败")
            
        task_dto = agent.getAnalyseTaskInfo(taskId)
        if not task_dto:
            if taskId is not None:
                agent.updateFileStatus(id, 2)
            raise AjaxResult.error("获取岗位信息失败，无法评分")

        taskInfo = TaskInfoDto.from_dict(task_dto)
        total_score = agent.calculate_total_score(talent_info, taskInfo)
        talent_info.totalScore = total_score

        talent_info.fileId = id
        talent_info.jobId = taskId
        # 保存解析结果
        save_success = agent.saveAnalyseTaskInfo(talent_info)
        
        # 根据保存结果更新状态
        if taskId is not None:
            if save_success:
                # 保存成功，更新状态为成功=0
                agent.updateFileStatus(id, 0)
            else:
                # 保存失败，更新状态为失败=2
                agent.updateFileStatus(id, 2)
                raise AjaxResult.error("保存分析结果失败")

        return AjaxResult.success(talent_info)
    except Exception as e:
        # 异常统一标记失败
        if 'agent' in locals() and taskId is not None:
            try:
                agent.updateFileStatus(id, 2)
            except Exception:
                pass
        print(f"Error in stream_chat: {str(e)}")
        return AjaxResult.error(str(e))

@router.get("/improveInfo")
async def improveInfo(evaluate: str):
    evaluate = unquote(evaluate)
    try:
        if not evaluate:
            raise AjaxResult.error()
        agent = ImproveInfoAgent()
        body = await agent.evaluateImproveInfo(evaluate)
        return AjaxResult.success(body)
    except Exception as e:
        print(f"Error in stream_chat: {str(e)}")
        return AjaxResult.error(data=0, message="完善信息失败")