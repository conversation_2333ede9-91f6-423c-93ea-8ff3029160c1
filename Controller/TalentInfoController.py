import asyncio
from fastapi import APIRouter
from openai import BaseModel
from urllib.parse import unquote

from Models.dto.TaskInfoDto import TaskInfoDto
from Models.AjaxResult import AjaxResult
from Services.TaskServer.TalentParseWorker import TalentParseWorker

# 尝试导入异步组件，如果失败则使用同步版本
try:
    from Agents.AsyncTalentAgent import async_talent_agent
    from Agents.AsyncImproveInfoAgent import async_improve_info_agent
    from Utils.TaskManager import task_manager, TaskStatus
    ASYNC_AVAILABLE = True
except ImportError as e:
    print(f"异步组件导入失败，使用同步版本: {e}")
    from Agents.TalentAgent import TalentAgent
    from Agents.TalentAgentProcessing import TalentAgentProcessing
    from Agents.ImproveInfoAgent import ImproveInfoAgent
    ASYNC_AVAILABLE = False

# 添加项目根目录到系统路径
router = APIRouter(prefix="/agentService/api/talent", tags=["talent"])


class TalentInfo(BaseModel):
    id: int = None
    content: str = None
    question: str = None


@router.post("/info")
async def chat(request: TalentInfo):
    try:
        if not request.question:
            raise AjaxResult.error()

        row = await async_talent_agent.chat_for_answer_async(request.question, request.id)
        return AjaxResult.success(row)
    except Exception as e:
        print(f"Error in stream_chat: {str(e)}")
        return AjaxResult.error(data=0, message=str(e))


# 简历解析（改进版本，带超时控制）
@router.get("/parse")
async def parse(fileUrl: str = None):
    try:
        if not fileUrl:
            raise AjaxResult.error("文件地址不能为空")

        # 创建异步任务，设置超时
        async def parse_task():
            # 调用文件解析方法
            content = await TalentParseWorker(fileUrl)._task()
            if not content:
                raise ValueError("文件解析失败")

            # 使用异步代理进行格式化
            talent_info = await async_talent_agent.formatting_async(content)
            return talent_info

        try:
            # 设置3秒超时
            talent_info = await asyncio.wait_for(parse_task(), timeout=3.0)
            return AjaxResult.success(talent_info)
        except asyncio.TimeoutError:
            # 超时则返回处理中状态
            return AjaxResult.success({
                "status": "processing",
                "message": "简历解析正在进行中，请稍后重试"
            })

    except Exception as e:
        print(f"Error in parse: {str(e)}")
        return AjaxResult.error(str(e))

# 简历评分
@router.get("/rating")
def rating():
    try:
        # TODO: 评分逻辑,直接传json评分结果
        return AjaxResult.success()
    except Exception as e:
        print(f"Error in stream_chat: {str(e)}")
        return AjaxResult.error(str(e))


# 简历解析（DTO版本，改进版本）
@router.get("/parseDto")
async def parseDto(fileUrl: str = None):
    try:
        if not fileUrl:
            raise AjaxResult.error("文件地址不能为空")

        # 创建异步任务，设置超时
        async def parse_dto_task():
            # 调用文件解析方法
            content = await TalentParseWorker(fileUrl)._task()
            if not content:
                raise ValueError("文件解析失败")

            # 使用异步代理进行格式化
            talent_info = await async_talent_agent.formatting_processing_async(content, boss_flag=True)
            return talent_info

        try:
            # 设置3秒超时
            talent_info = await asyncio.wait_for(parse_dto_task(), timeout=3.0)
            return AjaxResult.success(talent_info)
        except asyncio.TimeoutError:
            # 超时则返回处理中状态
            return AjaxResult.success({
                "status": "processing",
                "message": "简历解析正在进行中，请稍后重试"
            })

    except Exception as e:
        print(f"Error in parseDto: {str(e)}")
        return AjaxResult.error(str(e))

# 简历分析（改进版本，异步处理）
@router.get("/analyse")
async def analyse(fileUrl: str = None, taskId: int = None, id: int = None):
    try:
        if not fileUrl:
            raise AjaxResult.error("文件地址不能为空")

        # 标记解析中状态=1
        if taskId is not None:
            async_talent_agent.updateFileStatus(id, 1)

        # 创建异步分析任务
        async def analyse_task():
            # 调用文件解析方法
            content = await TalentParseWorker(fileUrl)._task()
            if not content:
                raise ValueError("文件解析失败")

            # 格式化并评分
            talent_info = await async_talent_agent.formatting_async(content)
            if talent_info is None:
                raise ValueError("简历解析失败")

            task_dto = async_talent_agent.getAnalyseTaskInfo(taskId)
            if not task_dto:
                raise ValueError("获取岗位信息失败，无法评分")

            taskInfo = TaskInfoDto.from_dict(task_dto)
            total_score = await async_talent_agent.calculate_total_score_async(talent_info, taskInfo)
            talent_info.totalScore = total_score

            talent_info.fileId = id
            talent_info.jobId = taskId

            # 保存解析结果
            save_success = async_talent_agent.saveAnalyseTaskInfo(talent_info)
            if not save_success:
                raise ValueError("保存分析结果失败")

            return talent_info

        try:
            # 设置5秒超时（分析比较复杂）
            talent_info = await asyncio.wait_for(analyse_task(), timeout=5.0)

            # 保存成功，更新状态为成功=0
            if taskId is not None:
                async_talent_agent.updateFileStatus(id, 0)

            return AjaxResult.success(talent_info)

        except asyncio.TimeoutError:
            # 超时则在后台继续处理，返回处理中状态
            asyncio.create_task(handle_analyse_background(fileUrl, taskId, id))
            return AjaxResult.success({
                "status": "processing",
                "message": "简历分析正在进行中，请稍后查询结果"
            })

    except Exception as e:
        # 异常统一标记失败
        if taskId is not None:
            try:
                async_talent_agent.updateFileStatus(id, 2)
            except Exception:
                pass
        print(f"Error in analyse: {str(e)}")
        return AjaxResult.error(str(e))

async def handle_analyse_background(fileUrl: str, taskId: int, id: int):
    """后台处理分析任务"""
    try:
        # 调用文件解析方法
        content = await TalentParseWorker(fileUrl)._task()
        if not content:
            async_talent_agent.updateFileStatus(id, 2)
            return

        # 格式化并评分
        talent_info = await async_talent_agent.formatting_async(content)
        if talent_info is None:
            async_talent_agent.updateFileStatus(id, 2)
            return

        task_dto = async_talent_agent.getAnalyseTaskInfo(taskId)
        if not task_dto:
            async_talent_agent.updateFileStatus(id, 2)
            return

        taskInfo = TaskInfoDto.from_dict(task_dto)
        total_score = await async_talent_agent.calculate_total_score_async(talent_info, taskInfo)
        talent_info.totalScore = total_score

        talent_info.fileId = id
        talent_info.jobId = taskId

        # 保存解析结果
        save_success = async_talent_agent.saveAnalyseTaskInfo(talent_info)
        if save_success:
            async_talent_agent.updateFileStatus(id, 0)
        else:
            async_talent_agent.updateFileStatus(id, 2)

    except Exception as e:
        print(f"Error in background analyse: {str(e)}")
        if taskId is not None:
            try:
                async_talent_agent.updateFileStatus(id, 2)
            except Exception:
                pass

# 新增：非阻塞简历解析接口
@router.post("/parse-async")
async def parse_async(request: TalentInfo):
    """提交简历解析任务，立即返回task_id"""
    try:
        if not request.content:  # 这里使用content字段作为fileUrl
            return AjaxResult.error("文件地址不能为空")

        # 定义解析任务函数
        def parse_task():
            import asyncio
            return asyncio.run(parse_resume_task(request.content))

        # 提交到任务管理器
        task_id = task_manager.submit_task(parse_task)

        return AjaxResult.success({
            "task_id": task_id,
            "message": "简历解析任务已提交，请使用task_id查询结果"
        })
    except Exception as e:
        print(f"Error in parse_async: {str(e)}")
        return AjaxResult.error(str(e))

async def parse_resume_task(file_url: str):
    """实际的简历解析任务"""
    # 调用文件解析方法
    content = await TalentParseWorker(file_url)._task()
    if not content:
        raise ValueError("文件解析失败")

    # 使用异步代理进行格式化
    talent_info = await async_talent_agent.formatting_async(content)
    return talent_info

@router.get("/task-status/{task_id}")
async def get_task_status(task_id: str):
    """查询任务状态"""
    try:
        task_info = task_manager.get_task_status(task_id)
        if not task_info:
            return AjaxResult.error("任务不存在")

        return AjaxResult.success({
            "task_id": task_info.task_id,
            "status": task_info.status.value,
            "result": task_info.result,
            "error": task_info.error,
            "progress": task_info.progress,
            "duration": task_info.duration
        })
    except Exception as e:
        print(f"Error in get_task_status: {str(e)}")
        return AjaxResult.error(str(e))

@router.get("/improveInfo")
async def improveInfo(evaluate: str):
    evaluate = unquote(evaluate)
    try:
        if not evaluate:
            raise AjaxResult.error()
        body = await async_improve_info_agent.evaluate_improve_info_async(evaluate)
        return AjaxResult.success(body)
    except Exception as e:
        print(f"Error in stream_chat: {str(e)}")
        return AjaxResult.error(data=0, message="完善信息失败")