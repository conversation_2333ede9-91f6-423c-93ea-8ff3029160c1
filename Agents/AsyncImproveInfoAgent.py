"""
异步ImproveInfoAgent代理类
将原有的同步LLM调用包装为异步操作
"""
import asyncio
from typing import Optional

from Agents.ImproveInfoAgent import ImproveInfoAgent
from Utils.AsyncConfig import async_config
from Utils.logs.LoggingConfig import logger


class AsyncImproveInfoAgent:
    """异步ImproveInfoAgent代理类"""
    
    def __init__(self):
        self._improve_agent = ImproveInfoAgent()
        self._executor = async_config.get_llm_executor()
    
    async def evaluate_improve_info_async(self, evaluate: str) -> Optional[str]:
        """异步版本的信息完善评估"""
        try:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(
                self._executor,
                self._improve_agent.evaluateImproveInfo,
                evaluate
            )
        except Exception as e:
            logger.error(f"Error in async evaluate_improve_info: {str(e)}")
            return None


# 全局异步代理实例
async_improve_info_agent = AsyncImproveInfoAgent()
