import re

from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import SystemMessagePromptTemplate, HumanMessagePromptTemplate, \
    ChatPromptTemplate
from langchain_core.runnables import RunnableLambda
from langchain_core.runnables.history import RunnableWithMessageHistory

from Configs.Config import SysConfig
from LLM.LLMManager import sys_llm_manager
from Models.agent.InterviewQuestions import InterviewQuestions, Questions
from Models.agent.TalentInfoProcessing import ContentInfo
from Utils.BaiduSearchUtil import BaiduSearchUtil
from Utils.CommonUtils import remove_think_tags
from Utils.logs.LoggingConfig import logger

input_sys_template = SystemMessagePromptTemplate.from_template(
    """
    你是一位经验丰富的人力资源管理专家，精通人才选拔与评估，对不同岗位的技能要求和素质标准有着深入的理解，擅长设计针对性强、覆盖面广的面试问题。
    背景:
        公司在招聘过程中需要对候选人进行全面评估，以确保其符合岗位要求和公司文化，因此需要设计一套科学、有效的面试问题。
        你将根据招聘岗位信息和岗位要求，生成一系列面试问题，以帮助公司评估候选人的能力和适应能力(主要是根据招聘岗位来出题，要求用来辅助)。
    技能：
        你具备组织行为学、心理学、人才测评技术以及行业知识的综合能力，能够根据不同岗位的特点和公司的战略需求，设计出高质量的面试问题。
    约束：
        1. 面试的每个问题必须跟所属能力和招聘岗位有关系，且每个问题的难度和广度都应适当均衡。
        2. 面试问题应符合法律法规和道德标准，避免歧视性问题，确保公平性和客观性，同时应具有一定的灵活性，以适应不同候选人的回答风格。
        3. 结合面试者简历中的个人经历去生成一些与面试者有关的问题。
    注意事项:
        1. 确保输出格式中的信息一定是跟主要类型有关联的,并且一个类型中必须有且只有两个问题。
        2. 每个问题类型的输出格式都是字符数组,必须严格按照规定的格式进行输出。
        3. 输出的信息必须是有效的JSON格式，一定不能包含任何额外的字符或空格。
    输出格式：{format_instructions}
    /no_think
   """
)
input_template = HumanMessagePromptTemplate.from_template(
    "招聘岗位：{position}"
    "题目类型：{type}"
    "岗位要求：{jobDesc}"
    "个人经历：{userInfo}"
)
# 基于百度结果查询出现的面试题目
search_template = SystemMessagePromptTemplate.from_template(
    """
    你是一位经验丰富的人力资源管理专家，精通人才选拔与评估，对不同岗位的技能要求和素质标准有着深入的理解，擅长设计针对性强、覆盖面广的面试问题。
    背景:
        公司在招聘过程中需要对候选人进行全面评估，因此需要设计一套科学、有效的面试问题。
    任务:
        根据【联网搜索结果】+【岗位基础信息】生成面试题。
    约束:
        1. 每个问题必须跟岗位实际要求相关，且难度适中。
        2. 符合法律法规和道德标准，避免歧视。
        3. 每个类型必须输出两个题目。
    输出格式：{format_instructions}
    /no_think
    """
)
search_input_template = HumanMessagePromptTemplate.from_template(
    "岗位名称：{position}\n\n{search_context}"
)


class InterviewQuestionsAgent:
    _llm_name: str | None = "DEFAULT"

    def __init__(self):
        self.__temperature = SysConfig["agents"]["kb_agent"]["temperature"]
        self.__output_parser = PydanticOutputParser(pydantic_object=InterviewQuestions)
        self.__output_parser_info = PydanticOutputParser(pydantic_object=Questions)
        self.__output_improve_info = PydanticOutputParser(pydantic_object=ContentInfo)

    # 根据LLM的名字设置LLM相关信息，包含对话信息
    def __set_llm(self, output_parser: PydanticOutputParser,
                  system_template: SystemMessagePromptTemplate,
                  human_template: HumanMessagePromptTemplate
                  ) -> RunnableWithMessageHistory | None:
        llm_helper = sys_llm_manager.get_generate_use_llm_helper()
        if llm_helper is None:
            return None
        llm_obj = llm_helper.get_llm_object(self.__temperature)

        prompt_template = ChatPromptTemplate.from_messages([
            system_template,
            human_template
        ]).partial(format_instructions=output_parser.get_format_instructions())

        runnable = (prompt_template | llm_obj
                    | RunnableLambda(lambda x: x if isinstance(x, str) else x.content)
                    | RunnableLambda(remove_think_tags)
                    | output_parser)
        return runnable

    async def _formatting(self, position: str, jobDesc: str, userInfo: str, search: bool) -> InterviewQuestions:
        try:
            # 调用LLM进行解析
            logger.info(f"内容：{position}")
            if search:
                talent_info = self.__set_llm(self.__output_parser, input_sys_template, input_template).invoke(
                    {"position": position, "type": "", "jobDesc": jobDesc, "userInfo": userInfo}
                )
            else:
                baidu_utils = BaiduSearchUtil()
                search_context = baidu_utils.build_prompt_from_search(position)
                talent_info = self.__set_llm(self.__output_parser, search_template, search_input_template).invoke(
                    {"position": position, "search_context": search_context}
                )
            logger.info(f"结果：{talent_info}")
            return talent_info
        except Exception as e:
            print(f"格式化失败: {str(e)}")
            raise e

    async def _formatting_info(self, position: str, type: str, jobDesc: str, userInfo: str,
                               search: bool) -> Questions:
        try:
            # 调用LLM进行解析
            logger.info(f"内容：{position}, {type}")
            if search:
                talent_info = self.__set_llm(self.__output_parser_info, input_sys_template, input_template).invoke(
                    {"position": position, "type": type, "jobDesc": jobDesc, "userInfo": userInfo}
                )
            else:
                baidu_utils = BaiduSearchUtil()
                search_context = baidu_utils.build_prompt_from_search(position)
                talent_info = self.__set_llm(self.__output_parser_info, search_template, search_input_template).invoke(
                    {"position": position, "search_context": search_context}
                )
            logger.info(f"结果：{talent_info}")
            return talent_info
        except Exception as e:
            print(f"格式化失败: {str(e)}")
            raise e

    def clean_resume_text(self, text):
        # 删除连续的空格，只保留一个空格
        text = re.sub(r' +', ' ', text)
        # 删除连续的换行符，只保留一个换行
        text = re.sub(r'\n+', '\n', text)
        # 删除行首和行尾的空格
        text = '\n'.join([line.strip() for line in text.split('\n')])
        # 删除空行
        text = '\n'.join([line for line in text.split('\n') if line.strip() != ''])
        return text
