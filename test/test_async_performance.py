"""
异步性能测试脚本
用于验证接口并发处理能力
"""

import asyncio
import aiohttp
import time
from typing import List


async def test_single_request(session: aiohttp.ClientSession, url: str, data: dict = None) -> dict:
    """测试单个请求"""
    start_time = time.time()
    try:
        if data:
            async with session.post(url, json=data) as response:
                result = await response.json()
        else:
            async with session.get(url) as response:
                result = await response.json()
        
        end_time = time.time()
        return {
            "success": True,
            "response_time": end_time - start_time,
            "status": response.status,
            "result": result
        }
    except Exception as e:
        end_time = time.time()
        return {
            "success": False,
            "response_time": end_time - start_time,
            "error": str(e)
        }


async def test_concurrent_requests(base_url: str, endpoint: str, num_requests: int = 5, data: dict = None):
    """测试并发请求"""
    url = f"{base_url}{endpoint}"
    
    async with aiohttp.ClientSession() as session:
        print(f"开始测试 {num_requests} 个并发请求到 {endpoint}")
        start_time = time.time()
        
        # 创建并发任务
        tasks = [test_single_request(session, url, data) for _ in range(num_requests)]
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 分析结果
        successful_requests = [r for r in results if r["success"]]
        failed_requests = [r for r in results if not r["success"]]
        
        if successful_requests:
            avg_response_time = sum(r["response_time"] for r in successful_requests) / len(successful_requests)
            max_response_time = max(r["response_time"] for r in successful_requests)
            min_response_time = min(r["response_time"] for r in successful_requests)
        else:
            avg_response_time = max_response_time = min_response_time = 0
        
        print(f"测试结果 - {endpoint}:")
        print(f"  总耗时: {total_time:.2f}s")
        print(f"  成功请求: {len(successful_requests)}/{num_requests}")
        print(f"  失败请求: {len(failed_requests)}")
        print(f"  平均响应时间: {avg_response_time:.2f}s")
        print(f"  最大响应时间: {max_response_time:.2f}s")
        print(f"  最小响应时间: {min_response_time:.2f}s")
        print(f"  并发效率: {num_requests/total_time:.2f} 请求/秒")
        
        if failed_requests:
            print("  失败原因:")
            for i, failed in enumerate(failed_requests[:3]):  # 只显示前3个失败
                print(f"    {i+1}. {failed['error']}")
        
        print("-" * 50)
        return results


async def main():
    """主测试函数"""
    base_url = "http://localhost:8000"
    
    # 测试不同的接口
    test_cases = [
        {
            "endpoint": "/agentService/api/talent/rating",
            "method": "GET",
            "data": None,
            "description": "简单接口测试"
        },
        {
            "endpoint": "/agentService/api/talent/info",
            "method": "POST", 
            "data": {"question": "测试问题", "id": 1},
            "description": "LLM处理接口测试"
        }
    ]
    
    print("开始异步性能测试...")
    print("=" * 60)
    
    for test_case in test_cases:
        print(f"测试场景: {test_case['description']}")
        await test_concurrent_requests(
            base_url, 
            test_case["endpoint"], 
            num_requests=3,  # 减少请求数量避免过载
            data=test_case["data"]
        )
        
        # 等待一段时间再进行下一个测试
        await asyncio.sleep(2)
    
    print("测试完成!")


if __name__ == "__main__":
    asyncio.run(main())
