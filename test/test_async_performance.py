"""
异步性能测试脚本
测试简历解析接口的并发处理能力
"""
import asyncio
import aiohttp
import time
import json
from typing import List, Dict


class PerformanceTest:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_simple_endpoint(self, endpoint: str = "/agentService/api/conversation/version") -> Dict:
        """测试简单接口的响应时间"""
        start_time = time.time()
        try:
            async with self.session.get(f"{self.base_url}{endpoint}") as response:
                result = await response.json()
                end_time = time.time()
                return {
                    "success": True,
                    "response_time": end_time - start_time,
                    "status_code": response.status,
                    "result": result
                }
        except Exception as e:
            end_time = time.time()
            return {
                "success": False,
                "response_time": end_time - start_time,
                "error": str(e)
            }
    
    async def test_resume_parse(self, file_url: str) -> Dict:
        """测试简历解析接口"""
        start_time = time.time()
        try:
            async with self.session.get(
                f"{self.base_url}/agentService/api/talent/parse",
                params={"fileUrl": file_url}
            ) as response:
                result = await response.json()
                end_time = time.time()
                return {
                    "success": True,
                    "response_time": end_time - start_time,
                    "status_code": response.status,
                    "result": result
                }
        except Exception as e:
            end_time = time.time()
            return {
                "success": False,
                "response_time": end_time - start_time,
                "error": str(e)
            }
    
    async def test_async_resume_parse(self, file_url: str) -> Dict:
        """测试异步简历解析接口"""
        start_time = time.time()
        try:
            # 提交任务
            async with self.session.post(
                f"{self.base_url}/agentService/api/talent/parse-async",
                json={"content": file_url}
            ) as response:
                submit_result = await response.json()
                submit_time = time.time()
                
                if not submit_result.get("success", False):
                    return {
                        "success": False,
                        "response_time": submit_time - start_time,
                        "error": "Failed to submit task"
                    }
                
                task_id = submit_result["data"]["task_id"]
                
                # 轮询查询结果
                max_wait = 30  # 最多等待30秒
                poll_interval = 2  # 每2秒查询一次
                
                for _ in range(max_wait // poll_interval):
                    await asyncio.sleep(poll_interval)
                    
                    async with self.session.get(
                        f"{self.base_url}/agentService/api/talent/task-status/{task_id}"
                    ) as status_response:
                        status_result = await status_response.json()
                        
                        if status_result.get("success", False):
                            status_data = status_result["data"]
                            if status_data["status"] == "completed":
                                end_time = time.time()
                                return {
                                    "success": True,
                                    "response_time": end_time - start_time,
                                    "submit_time": submit_time - start_time,
                                    "total_time": end_time - start_time,
                                    "result": status_data["result"]
                                }
                            elif status_data["status"] == "failed":
                                end_time = time.time()
                                return {
                                    "success": False,
                                    "response_time": end_time - start_time,
                                    "error": status_data.get("error", "Task failed")
                                }
                
                # 超时
                end_time = time.time()
                return {
                    "success": False,
                    "response_time": end_time - start_time,
                    "error": "Timeout waiting for task completion"
                }
                
        except Exception as e:
            end_time = time.time()
            return {
                "success": False,
                "response_time": end_time - start_time,
                "error": str(e)
            }
    
    async def concurrent_test(self, test_func, *args, concurrency: int = 5) -> List[Dict]:
        """并发测试"""
        tasks = [test_func(*args) for _ in range(concurrency)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                processed_results.append({
                    "success": False,
                    "error": str(result),
                    "response_time": 0
                })
            else:
                processed_results.append(result)
        
        return processed_results


async def main():
    """主测试函数"""
    print("开始异步性能测试...")
    
    # 测试用的文件URL（请替换为实际的文件URL）
    test_file_url = "https://example.com/test-resume.pdf"
    
    async with PerformanceTest() as tester:
        print("\n1. 测试简单接口响应时间...")
        simple_results = await tester.concurrent_test(
            tester.test_simple_endpoint,
            "/agentService/api/conversation/version",
            concurrency=10
        )
        
        success_count = sum(1 for r in simple_results if r["success"])
        avg_time = sum(r["response_time"] for r in simple_results if r["success"]) / max(success_count, 1)
        print(f"简单接口测试结果: {success_count}/10 成功, 平均响应时间: {avg_time:.3f}秒")
        
        print("\n2. 测试简历解析接口并发能力...")
        print("注意：这个测试需要有效的文件URL，如果没有请跳过")
        
        # 如果有实际的文件URL，可以取消注释下面的代码进行测试
        # parse_results = await tester.concurrent_test(
        #     tester.test_resume_parse,
        #     test_file_url,
        #     concurrency=3
        # )
        # 
        # success_count = sum(1 for r in parse_results if r["success"])
        # avg_time = sum(r["response_time"] for r in parse_results if r["success"]) / max(success_count, 1)
        # print(f"简历解析测试结果: {success_count}/3 成功, 平均响应时间: {avg_time:.3f}秒")
        
        print("\n3. 测试异步简历解析接口...")
        # async_parse_results = await tester.concurrent_test(
        #     tester.test_async_resume_parse,
        #     test_file_url,
        #     concurrency=3
        # )
        # 
        # success_count = sum(1 for r in async_parse_results if r["success"])
        # avg_submit_time = sum(r.get("submit_time", 0) for r in async_parse_results if r["success"]) / max(success_count, 1)
        # print(f"异步简历解析测试结果: {success_count}/3 成功, 平均提交时间: {avg_submit_time:.3f}秒")
        
        print("\n测试完成！")
        print("\n测试说明：")
        print("- 简单接口应该能够快速响应，即使在简历解析进行时")
        print("- 异步简历解析接口应该能够立即返回task_id")
        print("- 多个简历解析任务应该能够并发处理")


if __name__ == "__main__":
    asyncio.run(main())
