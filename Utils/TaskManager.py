"""
任务管理器
用于管理长时间运行的异步任务，避免阻塞主线程
"""
import asyncio
import time
import uuid
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from enum import Enum
from typing import Dict, Optional, Any, Callable
import threading

from Utils.logs.LoggingConfig import logger


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待中
    RUNNING = "running"      # 运行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 失败
    CANCELLED = "cancelled"  # 已取消


@dataclass
class TaskInfo:
    """任务信息"""
    task_id: str
    status: TaskStatus
    result: Optional[Any] = None
    error: Optional[str] = None
    progress: float = 0.0
    start_time: float = 0.0
    end_time: Optional[float] = None
    
    @property
    def duration(self) -> Optional[float]:
        """任务持续时间"""
        if self.end_time:
            return self.end_time - self.start_time
        elif self.start_time:
            return time.time() - self.start_time
        return None


class TaskManager:
    """任务管理器"""
    
    def __init__(self, max_workers: int = 5, cleanup_interval: int = 300):
        self.max_workers = max_workers
        self.cleanup_interval = cleanup_interval
        self._tasks: Dict[str, TaskInfo] = {}
        self._executor = ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="TaskManager")
        self._lock = threading.Lock()
        self._cleanup_task = None
        
        # 启动清理任务
        self._start_cleanup_task()
        
        logger.info(f"任务管理器初始化完成，最大工作线程: {max_workers}")
    
    def _start_cleanup_task(self):
        """启动清理任务"""
        async def cleanup_loop():
            while True:
                try:
                    await asyncio.sleep(self.cleanup_interval)
                    self._cleanup_old_tasks()
                except Exception as e:
                    logger.error(f"清理任务出错: {e}")
        
        try:
            loop = asyncio.get_event_loop()
            self._cleanup_task = loop.create_task(cleanup_loop())
        except RuntimeError:
            # 如果没有运行的事件循环，稍后再启动
            pass
    
    def _cleanup_old_tasks(self, max_age: int = 3600):
        """清理旧任务（默认1小时）"""
        current_time = time.time()
        with self._lock:
            to_remove = []
            for task_id, task_info in self._tasks.items():
                if (task_info.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED] and
                    task_info.end_time and current_time - task_info.end_time > max_age):
                    to_remove.append(task_id)
            
            for task_id in to_remove:
                del self._tasks[task_id]
                
            if to_remove:
                logger.info(f"清理了 {len(to_remove)} 个过期任务")
    
    def submit_task(self, func: Callable, *args, **kwargs) -> str:
        """提交任务"""
        task_id = str(uuid.uuid4())
        
        with self._lock:
            task_info = TaskInfo(
                task_id=task_id,
                status=TaskStatus.PENDING,
                start_time=time.time()
            )
            self._tasks[task_id] = task_info
        
        # 提交到线程池
        future = self._executor.submit(self._run_task, task_id, func, *args, **kwargs)
        
        logger.info(f"任务已提交: {task_id}")
        return task_id
    
    def _run_task(self, task_id: str, func: Callable, *args, **kwargs):
        """运行任务"""
        try:
            with self._lock:
                if task_id in self._tasks:
                    self._tasks[task_id].status = TaskStatus.RUNNING
                    self._tasks[task_id].progress = 0.1
            
            logger.info(f"开始执行任务: {task_id}")
            
            # 执行任务
            result = func(*args, **kwargs)
            
            # 更新任务状态
            with self._lock:
                if task_id in self._tasks:
                    self._tasks[task_id].status = TaskStatus.COMPLETED
                    self._tasks[task_id].result = result
                    self._tasks[task_id].progress = 1.0
                    self._tasks[task_id].end_time = time.time()
            
            logger.info(f"任务执行完成: {task_id}")
            
        except Exception as e:
            logger.error(f"任务执行失败 {task_id}: {str(e)}")
            
            with self._lock:
                if task_id in self._tasks:
                    self._tasks[task_id].status = TaskStatus.FAILED
                    self._tasks[task_id].error = str(e)
                    self._tasks[task_id].end_time = time.time()
    
    def get_task_status(self, task_id: str) -> Optional[TaskInfo]:
        """获取任务状态"""
        with self._lock:
            return self._tasks.get(task_id)
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self._lock:
            if task_id in self._tasks and self._tasks[task_id].status == TaskStatus.PENDING:
                self._tasks[task_id].status = TaskStatus.CANCELLED
                self._tasks[task_id].end_time = time.time()
                return True
        return False
    
    def get_all_tasks(self) -> Dict[str, TaskInfo]:
        """获取所有任务"""
        with self._lock:
            return self._tasks.copy()
    
    def shutdown(self):
        """关闭任务管理器"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
        
        self._executor.shutdown(wait=True)
        logger.info("任务管理器已关闭")


# 全局任务管理器实例
task_manager = TaskManager(max_workers=5)
