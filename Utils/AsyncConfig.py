"""
异步配置管理器
提供全局的线程池和并发控制
"""
import asyncio
import atexit
from concurrent.futures import ThreadPoolExecutor
from typing import Optional, Callable, Any

from Configs.Config import SysConfig
from Utils.logs.LoggingConfig import logger


class AsyncConfig:
    """全局异步配置管理器"""
    
    def __init__(self):
        # 从配置文件读取并发设置，如果没有则使用默认值
        self.max_llm_workers = SysConfig.get("async_config", {}).get("max_llm_workers", 5)
        self.max_file_workers = SysConfig.get("async_config", {}).get("max_file_workers", 3)
        self.max_download_workers = SysConfig.get("async_config", {}).get("max_download_workers", 5)
        
        # 创建全局线程池
        self._llm_executor: Optional[ThreadPoolExecutor] = None
        self._file_executor: Optional[ThreadPoolExecutor] = None
        self._download_executor: Optional[ThreadPoolExecutor] = None
        
        # 创建信号量来限制并发
        self._llm_semaphore = asyncio.Semaphore(self.max_llm_workers)
        self._file_semaphore = asyncio.Semaphore(self.max_file_workers)
        self._download_semaphore = asyncio.Semaphore(self.max_download_workers)
        
        logger.info(f"异步配置初始化完成 - LLM工作线程: {self.max_llm_workers}, "
                   f"文件工作线程: {self.max_file_workers}, "
                   f"下载工作线程: {self.max_download_workers}")
    
    def get_llm_executor(self) -> ThreadPoolExecutor:
        """获取LLM线程池"""
        if self._llm_executor is None:
            self._llm_executor = ThreadPoolExecutor(
                max_workers=self.max_llm_workers,
                thread_name_prefix="LLM-Pool"
            )
        return self._llm_executor
    
    def get_file_executor(self) -> ThreadPoolExecutor:
        """获取文件处理线程池"""
        if self._file_executor is None:
            self._file_executor = ThreadPoolExecutor(
                max_workers=self.max_file_workers,
                thread_name_prefix="File-Pool"
            )
        return self._file_executor
    
    def get_download_executor(self) -> ThreadPoolExecutor:
        """获取下载线程池"""
        if self._download_executor is None:
            self._download_executor = ThreadPoolExecutor(
                max_workers=self.max_download_workers,
                thread_name_prefix="Download-Pool"
            )
        return self._download_executor
    
    async def run_with_llm_limit(self, func: Callable, *args, **kwargs) -> Any:
        """在LLM信号量限制下运行函数"""
        async with self._llm_semaphore:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self.get_llm_executor(), func, *args, **kwargs)
    
    async def run_with_file_limit(self, func: Callable, *args, **kwargs) -> Any:
        """在文件处理信号量限制下运行函数"""
        async with self._file_semaphore:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self.get_file_executor(), func, *args, **kwargs)
    
    async def run_with_download_limit(self, func: Callable, *args, **kwargs) -> Any:
        """在下载信号量限制下运行函数"""
        async with self._download_semaphore:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self.get_download_executor(), func, *args, **kwargs)
    
    def cleanup(self):
        """清理资源"""
        try:
            if self._llm_executor:
                self._llm_executor.shutdown(wait=True)
                logger.info("LLM线程池已关闭")
            
            if self._file_executor:
                self._file_executor.shutdown(wait=True)
                logger.info("文件处理线程池已关闭")
            
            if self._download_executor:
                self._download_executor.shutdown(wait=True)
                logger.info("下载线程池已关闭")
                
        except Exception as e:
            logger.error(f"清理异步资源时出错: {e}")


# 全局实例
async_config = AsyncConfig()

# 注册清理函数
atexit.register(async_config.cleanup)
