import os
from typing import Optional
import asyncio

import requests
import aiohttp

from Utils.logs.LoggingConfig import logger


class FileDownloader:
    def __init__(self, temp_dir: str):
        """初始化文件下载器
        Args:
            temp_dir: 临时文件存储目录
        """
        self.temp_dir = temp_dir
        os.makedirs(temp_dir, exist_ok=True)

    def download(self, file_url: str) -> Optional[str]:
        """下载文件到临时目录
        Args:
            file_url: 文件URL
            
        Returns:
            str: 下载文件的本地路径，下载失败返回None
        """
        try:
            # 从URL中获取文件名
            file_name = os.path.basename(file_url)
            temp_path = os.path.join(self.temp_dir, file_name)

            # 使用requests下载文件
            response = requests.get(file_url, stream=True)
            response.raise_for_status()

            # 保存文件
            with open(temp_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            logger.info(f"文件下载成功: {file_url} -> {temp_path}")
            return temp_path

        except requests.exceptions.RequestException as e:
            logger.error(f"下载文件失败 {file_url}: {e}")
            return None
        except Exception as e:
            logger.error(f"处理文件失败 {file_url}: {e}")
            return None

    def remove(self, file_url: str) -> Optional[str]:
        """删除临时文件
        Args:
            file_url: 文件URL

        Returns:
            str: 删除文件的本地路径，删除失败返回None
        """
        try:
            # 从URL中获取文件名
            file_name = os.path.basename(file_url)
            temp_path = os.path.join(self.temp_dir, file_name)
            # 检查文件是否存在
            if os.path.exists(temp_path):
                os.remove(temp_path)
                logger.info(f"文件删除成功: {temp_path}")
                return temp_path
            else:
                logger.info(f"文件不存在: {temp_path}")
                return None
        except Exception as e:
            logger.error(f"删除文件失败 {file_url}: {e}")
            return None


    async def download_async(self, file_url: str) -> Optional[str]:
        """异步下载文件到临时目录
        Args:
            file_url: 文件URL

        Returns:
            str: 下载文件的本地路径，下载失败返回None
        """
        try:
            # 从URL中获取文件名
            file_name = os.path.basename(file_url)
            temp_path = os.path.join(self.temp_dir, file_name)

            # 使用aiohttp异步下载文件
            async with aiohttp.ClientSession() as session:
                async with session.get(file_url) as response:
                    response.raise_for_status()

                    # 异步保存文件
                    with open(temp_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            f.write(chunk)

            logger.info(f"异步文件下载成功: {file_url} -> {temp_path}")
            return temp_path

        except aiohttp.ClientError as e:
            logger.error(f"异步下载文件失败 {file_url}: {e}")
            return None
        except Exception as e:
            logger.error(f"异步处理文件失败 {file_url}: {e}")
            return None

    async def download_in_thread(self, file_url: str) -> Optional[str]:
        """在线程池中执行同步下载
        Args:
            file_url: 文件URL

        Returns:
            str: 下载文件的本地路径，下载失败返回None
        """
        try:
            # 导入异步配置（避免循环导入）
            from Utils.AsyncConfig import async_config
            return await async_config.run_with_download_limit(self.download, file_url)
        except Exception as e:
            logger.error(f"线程池下载文件失败 {file_url}: {e}")
            return None

    def cleanup(self):
        """清理临时文件和资源"""
        try:
            if hasattr(self, 'temp_dir') and os.path.exists(self.temp_dir):
                import shutil
                shutil.rmtree(self.temp_dir)
                os.makedirs(self.temp_dir, exist_ok=True)
        except Exception as e:
            logger.error(f"Error cleaning up FileDownloader: {e}")
